/**
 * 数据库服务
 * 提供数据存储、查询、更新等功能
 */

const fs = require('fs').promises;
const path = require('path');

class DatabaseService {
  constructor() {
    this.dataDir = path.join(__dirname, '../data');
    this.ensureDataDirectory();
  }

  /**
   * 确保数据目录存在
   */
  async ensureDataDirectory() {
    try {
      await fs.access(this.dataDir);
    } catch (error) {
      await fs.mkdir(this.dataDir, { recursive: true });
    }
  }

  /**
   * 保存文章数据
   * @param {Object} articleData - 文章数据
   * @returns {Promise<string>} 文章ID
   */
  async saveArticle(articleData) {
    const articleId = this.generateId();
    const timestamp = new Date().toISOString();
    
    const article = {
      id: articleId,
      title: articleData.title || 'Untitled',
      content: articleData.content,
      source: articleData.source || 'manual',
      createdAt: timestamp,
      updatedAt: timestamp,
      metadata: {
        wordCount: articleData.wordCount || 0,
        sentenceCount: articleData.sentenceCount || 0,
        difficulty: articleData.difficulty || 'unknown'
      }
    };
    
    const filePath = path.join(this.dataDir, `article_${articleId}.json`);
    await fs.writeFile(filePath, JSON.stringify(article, null, 2));
    
    // 更新文章索引
    await this.updateArticleIndex(article);
    
    return articleId;
  }

  /**
   * 获取文章数据
   * @param {string} articleId - 文章ID
   * @returns {Promise<Object|null>} 文章数据
   */
  async getArticle(articleId) {
    try {
      const filePath = path.join(this.dataDir, `article_${articleId}.json`);
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取所有文章列表
   * @returns {Promise<Array>} 文章列表
   */
  async getAllArticles() {
    try {
      const indexPath = path.join(this.dataDir, 'articles_index.json');
      const data = await fs.readFile(indexPath, 'utf8');
      const index = JSON.parse(data);
      return index.articles || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 保存单词数据
   * @param {string} articleId - 文章ID
   * @param {Array} words - 单词数组
   * @returns {Promise<void>}
   */
  async saveWords(articleId, words) {
    const filePath = path.join(this.dataDir, `words_${articleId}.json`);
    const wordsData = {
      articleId,
      words,
      createdAt: new Date().toISOString(),
      totalCount: words.length
    };
    
    await fs.writeFile(filePath, JSON.stringify(wordsData, null, 2));
  }

  /**
   * 获取单词数据
   * @param {string} articleId - 文章ID
   * @returns {Promise<Array>} 单词数组
   */
  async getWords(articleId) {
    try {
      const filePath = path.join(this.dataDir, `words_${articleId}.json`);
      const data = await fs.readFile(filePath, 'utf8');
      const wordsData = JSON.parse(data);
      return wordsData.words || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 保存句子数据
   * @param {string} articleId - 文章ID
   * @param {Array} sentences - 句子数组
   * @returns {Promise<void>}
   */
  async saveSentences(articleId, sentences) {
    const filePath = path.join(this.dataDir, `sentences_${articleId}.json`);
    const sentencesData = {
      articleId,
      sentences,
      createdAt: new Date().toISOString(),
      totalCount: sentences.length
    };
    
    await fs.writeFile(filePath, JSON.stringify(sentencesData, null, 2));
  }

  /**
   * 获取句子数据
   * @param {string} articleId - 文章ID
   * @returns {Promise<Array>} 句子数组
   */
  async getSentences(articleId) {
    try {
      const filePath = path.join(this.dataDir, `sentences_${articleId}.json`);
      const data = await fs.readFile(filePath, 'utf8');
      const sentencesData = JSON.parse(data);
      return sentencesData.sentences || [];
    } catch (error) {
      return [];
    }
  }

  /**
   * 保存分析结果
   * @param {string} articleId - 文章ID
   * @param {Object} analysis - 分析结果
   * @returns {Promise<void>}
   */
  async saveAnalysis(articleId, analysis) {
    const filePath = path.join(this.dataDir, `analysis_${articleId}.json`);
    const analysisData = {
      articleId,
      analysis,
      createdAt: new Date().toISOString()
    };
    
    await fs.writeFile(filePath, JSON.stringify(analysisData, null, 2));
  }

  /**
   * 获取分析结果
   * @param {string} articleId - 文章ID
   * @returns {Promise<Object|null>} 分析结果
   */
  async getAnalysis(articleId) {
    try {
      const filePath = path.join(this.dataDir, `analysis_${articleId}.json`);
      const data = await fs.readFile(filePath, 'utf8');
      const analysisData = JSON.parse(data);
      return analysisData.analysis || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 删除文章及相关数据
   * @param {string} articleId - 文章ID
   * @returns {Promise<boolean>} 删除是否成功
   */
  async deleteArticle(articleId) {
    try {
      const files = [
        `article_${articleId}.json`,
        `words_${articleId}.json`,
        `sentences_${articleId}.json`,
        `analysis_${articleId}.json`
      ];
      
      for (const file of files) {
        const filePath = path.join(this.dataDir, file);
        try {
          await fs.unlink(filePath);
        } catch (error) {
          // 文件不存在，忽略错误
        }
      }
      
      // 从索引中移除
      await this.removeFromArticleIndex(articleId);
      
      return true;
    } catch (error) {
      console.error('删除文章失败:', error);
      return false;
    }
  }

  /**
   * 更新文章索引
   * @param {Object} article - 文章数据
   * @returns {Promise<void>}
   */
  async updateArticleIndex(article) {
    const indexPath = path.join(this.dataDir, 'articles_index.json');
    
    let index = { articles: [] };
    try {
      const data = await fs.readFile(indexPath, 'utf8');
      index = JSON.parse(data);
    } catch (error) {
      // 索引文件不存在，使用默认值
    }
    
    // 移除已存在的同ID文章
    index.articles = index.articles.filter(a => a.id !== article.id);
    
    // 添加新文章到索引
    index.articles.unshift({
      id: article.id,
      title: article.title,
      createdAt: article.createdAt,
      updatedAt: article.updatedAt,
      wordCount: article.metadata.wordCount,
      difficulty: article.metadata.difficulty
    });
    
    // 限制索引大小，只保留最新的100篇文章
    if (index.articles.length > 100) {
      index.articles = index.articles.slice(0, 100);
    }
    
    await fs.writeFile(indexPath, JSON.stringify(index, null, 2));
  }

  /**
   * 从文章索引中移除
   * @param {string} articleId - 文章ID
   * @returns {Promise<void>}
   */
  async removeFromArticleIndex(articleId) {
    const indexPath = path.join(this.dataDir, 'articles_index.json');
    
    try {
      const data = await fs.readFile(indexPath, 'utf8');
      const index = JSON.parse(data);
      
      index.articles = index.articles.filter(a => a.id !== articleId);
      
      await fs.writeFile(indexPath, JSON.stringify(index, null, 2));
    } catch (error) {
      // 索引文件不存在或其他错误，忽略
    }
  }

  /**
   * 搜索文章
   * @param {string} query - 搜索关键词
   * @returns {Promise<Array>} 搜索结果
   */
  async searchArticles(query) {
    const articles = await this.getAllArticles();
    
    if (!query) return articles;
    
    const searchTerm = query.toLowerCase();
    const results = [];
    
    for (const articleInfo of articles) {
      const article = await this.getArticle(articleInfo.id);
      if (article) {
        const titleMatch = article.title.toLowerCase().includes(searchTerm);
        const contentMatch = article.content.toLowerCase().includes(searchTerm);
        
        if (titleMatch || contentMatch) {
          results.push({
            ...articleInfo,
            relevance: titleMatch ? 2 : 1 // 标题匹配权重更高
          });
        }
      }
    }
    
    // 按相关性排序
    return results.sort((a, b) => b.relevance - a.relevance);
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 获取统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getStatistics() {
    const articles = await this.getAllArticles();
    
    const stats = {
      totalArticles: articles.length,
      totalWords: 0,
      difficultyDistribution: {
        beginner: 0,
        intermediate: 0,
        advanced: 0,
        unknown: 0
      },
      recentActivity: articles.slice(0, 5)
    };
    
    articles.forEach(article => {
      stats.totalWords += article.wordCount || 0;
      const difficulty = article.difficulty || 'unknown';
      if (stats.difficultyDistribution[difficulty] !== undefined) {
        stats.difficultyDistribution[difficulty]++;
      }
    });
    
    return stats;
  }
}

module.exports = DatabaseService;