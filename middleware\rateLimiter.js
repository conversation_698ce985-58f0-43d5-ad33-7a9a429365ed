/**
 * 限流中间件
 */

const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const logger = require('../utils/logger');

// 基础限流配置
const createRateLimiter = (options = {}) => {
  const defaultOptions = {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '15') * 60 * 1000, // 15分钟
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // 最大请求数
    message: {
      error: 'Too many requests from this IP, please try again later.',
      code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      logger.warn(`Rate limit exceeded for IP: ${req.ip}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        url: req.originalUrl
      });
      
      res.status(429).json({
        error: 'Too many requests from this IP, please try again later.',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.round(options.windowMs / 1000)
      });
    },
    skip: (req) => {
      // 跳过健康检查和静态资源
      return req.path === '/health' || req.path.startsWith('/static/');
    }
  };

  return rateLimit({ ...defaultOptions, ...options });
};

// API限流
const apiLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100个请求
  message: {
    error: 'Too many API requests, please try again later.',
    code: 'API_RATE_LIMIT_EXCEEDED'
  }
});

// 严格限流（用于敏感操作）
const strictLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 10, // 每个IP最多10个请求
  message: {
    error: 'Too many requests for this operation, please try again later.',
    code: 'STRICT_RATE_LIMIT_EXCEEDED'
  }
});

// 登录限流
const loginLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 每个IP最多5次登录尝试
  skipSuccessfulRequests: true, // 成功的请求不计入限制
  message: {
    error: 'Too many login attempts, please try again later.',
    code: 'LOGIN_RATE_LIMIT_EXCEEDED'
  }
});

// 注册限流
const registerLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 每个IP最多3次注册尝试
  message: {
    error: 'Too many registration attempts, please try again later.',
    code: 'REGISTER_RATE_LIMIT_EXCEEDED'
  }
});

// AI API限流
const aiLimiter = createRateLimiter({
  windowMs: 60 * 1000, // 1分钟
  max: 20, // 每分钟最多20个AI请求
  message: {
    error: 'Too many AI requests, please try again later.',
    code: 'AI_RATE_LIMIT_EXCEEDED'
  }
});

// 慢速响应中间件（逐渐增加延迟）
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15分钟
  delayAfter: 50, // 50个请求后开始延迟
  delayMs: 500, // 每个请求增加500ms延迟
  maxDelayMs: 20000, // 最大延迟20秒
  skip: (req) => {
    return req.path === '/health' || req.path.startsWith('/static/');
  },
  onLimitReached: (req, res, options) => {
    logger.warn(`Speed limit reached for IP: ${req.ip}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl
    });
  }
});

// 动态限流（根据用户类型调整）
const dynamicLimiter = (req, res, next) => {
  let maxRequests = 100; // 默认限制
  
  if (req.user) {
    // 已登录用户更高的限制
    maxRequests = 200;
    
    if (req.user.isPremium) {
      // 付费用户更高的限制
      maxRequests = 500;
    }
  }
  
  const limiter = createRateLimiter({
    max: maxRequests,
    keyGenerator: (req) => {
      // 已登录用户使用用户ID，未登录使用IP
      return req.user ? `user:${req.user.id}` : `ip:${req.ip}`;
    }
  });
  
  return limiter(req, res, next);
};

// 自定义限流存储（使用Redis）
class RedisStore {
  constructor(redisClient) {
    this.client = redisClient;
    this.prefix = 'rate_limit:';
  }

  async increment(key) {
    const fullKey = this.prefix + key;
    const current = await this.client.incr(fullKey);
    
    if (current === 1) {
      // 第一次访问，设置过期时间
      await this.client.expire(fullKey, 900); // 15分钟
    }
    
    return {
      totalHits: current,
      resetTime: new Date(Date.now() + 900000) // 15分钟后
    };
  }

  async decrement(key) {
    const fullKey = this.prefix + key;
    const current = await this.client.decr(fullKey);
    return Math.max(0, current);
  }

  async resetKey(key) {
    const fullKey = this.prefix + key;
    await this.client.del(fullKey);
  }
}

// 创建Redis存储实例的工厂函数
const createRedisStore = (redisClient) => {
  if (redisClient) {
    return new RedisStore(redisClient);
  }
  return undefined; // 使用默认内存存储
};

module.exports = {
  createRateLimiter,
  apiLimiter,
  strictLimiter,
  loginLimiter,
  registerLimiter,
  aiLimiter,
  speedLimiter,
  dynamicLimiter,
  createRedisStore
};
