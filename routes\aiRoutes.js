const express = require('express');
const axios = require('axios');
const crypto = require('crypto');
const router = express.Router();

// 智谱AI配置
const ZHIPU_API_KEY = '********************************.2xtdpzi3Be895EU2';
const ZHIPU_BASE_URL = 'https://open.bigmodel.cn/api/paas/v4/';

// 智谱AI API调用函数
async function callZhipuAPI(messages, model = 'glm-4-flash') {
    try {
        console.log('正在调用智谱AI API...');
        console.log('API URL:', `${ZHIPU_BASE_URL}chat/completions`);
        console.log('Model:', model);
        console.log('Messages:', JSON.stringify(messages, null, 2));
        
        const requestData = {
            model: model,
            messages: messages,
            temperature: 0.7,
            max_tokens: 1000,
            stream: false
        };
        
        console.log('请求数据:', JSON.stringify(requestData, null, 2));
        
        const response = await axios.post(
            `${ZHIPU_BASE_URL}chat/completions`,
            requestData,
            {
                headers: {
                    'Authorization': `Bearer ${ZHIPU_API_KEY}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                timeout: 30000,
                validateStatus: function (status) {
                    return status < 500; // 接受所有小于500的状态码
                }
            }
        );
        
        console.log('智谱AI API响应状态:', response.status);
        console.log('智谱AI API响应数据:', JSON.stringify(response.data, null, 2));
        
        if (response.status !== 200) {
            throw new Error(`API返回错误状态: ${response.status}, 响应: ${JSON.stringify(response.data)}`);
        }
        
        if (!response.data.choices || !response.data.choices[0] || !response.data.choices[0].message) {
            throw new Error('API响应格式错误: ' + JSON.stringify(response.data));
        }
        
        return response.data.choices[0].message.content;
    } catch (error) {
        console.error('智谱AI API调用失败:');
        console.error('错误类型:', error.constructor.name);
        console.error('错误消息:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
        }
        if (error.code) {
            console.error('错误代码:', error.code);
        }
        throw error;
    }
}

// AI搜索英文文章
router.post('/search-articles', async (req, res) => {
  try {
    const { query, difficulty = 'intermediate' } = req.body;
    
    if (!query) {
      return res.status(400).json({ error: '搜索关键词不能为空' });
    }

    // 使用OpenAI API搜索相关文章
    const openaiResponse = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `你是一个英语学习助手。请根据用户的搜索关键词，生成一篇适合${difficulty}水平学习者的英文文章。文章应该：
1. 包含用户搜索的关键词
2. 长度在200-400词之间
3. 语法结构清晰，适合学习
4. 内容有趣且有教育意义
5. 包含一些常用的词汇和短语`
          },
          {
            role: 'user',
            content: `请写一篇关于"${query}"的英文文章`
          }
        ],
        max_tokens: 800,
        temperature: 0.7
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const article = openaiResponse.data.choices[0].message.content;
    
    res.json({
      success: true,
      data: {
        article,
        query,
        difficulty,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('AI搜索错误:', error.message);
    
    // 如果API调用失败，返回模拟数据
    const mockArticle = `The Importance of ${req.body.query}

${req.body.query} plays a crucial role in our daily lives. Understanding its significance can help us make better decisions and improve our overall well-being.

First, ${req.body.query} affects how we interact with others. When we have a good grasp of this concept, we can communicate more effectively and build stronger relationships.

Second, ${req.body.query} influences our personal growth. By learning about it, we develop new skills and expand our knowledge base. This continuous learning process keeps our minds active and engaged.

Finally, ${req.body.query} has practical applications in many fields. Whether in business, education, or personal development, understanding this topic can lead to better outcomes and success.

In conclusion, ${req.body.query} is more than just a concept – it's a valuable tool for improving our lives and achieving our goals.`;

    res.json({
      success: true,
      data: {
        article: mockArticle,
        query: req.body.query,
        difficulty: req.body.difficulty || 'intermediate',
        timestamp: new Date().toISOString(),
        note: 'This is a mock response due to API limitations'
      }
    });
  }
});

// 语法分析
router.post('/analyze-grammar', async (req, res) => {
  try {
    const { word, sentence } = req.body;
    
    if (!word) {
      return res.status(400).json({ error: '单词不能为空' });
    }

    // 使用OpenAI API进行语法分析
    const openaiResponse = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个英语语法专家。请分析给定单词的语法信息，包括词性、常用短语、例句等。请用中文回答。'
          },
          {
            role: 'user',
            content: `请分析单词"${word}"${sentence ? `在句子"${sentence}"中` : ''}的语法信息，包括：
1. 词性和基本含义
2. 常用短语和搭配
3. 使用例句
4. 语法注意事项`
          }
        ],
        max_tokens: 500,
        temperature: 0.3
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const analysis = openaiResponse.data.choices[0].message.content;
    
    res.json({
      success: true,
      data: {
        word,
        sentence,
        analysis,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('语法分析错误:', error.message);
    
    // 返回模拟数据
    const mockAnalysis = `单词"${req.body.word}"的语法分析：

1. 词性和含义：
   - 这是一个常用的英语单词
   - 具有多种词性和含义

2. 常用短语：
   - ${req.body.word} + 介词短语
   - 形容词 + ${req.body.word}

3. 例句：
   - This is an example sentence with ${req.body.word}.
   - ${req.body.word} is important in English learning.

4. 语法注意事项：
   - 注意单复数形式
   - 注意时态变化
   - 注意搭配使用`;

    res.json({
      success: true,
      data: {
        word: req.body.word,
        sentence: req.body.sentence,
        analysis: mockAnalysis,
        timestamp: new Date().toISOString(),
        note: 'This is a mock response due to API limitations'
      }
    });
  }
});

// 单词查询
router.post('/word-lookup', async (req, res) => {
  try {
    const { word } = req.body;
    
    if (!word) {
      return res.status(400).json({ error: '单词不能为空' });
    }

    // 使用免费的词典API
    const dictionaryResponse = await axios.get(
      `https://api.dictionaryapi.dev/api/v2/entries/en/${word.toLowerCase()}`,
      {
        timeout: 10000
      }
    );

    const wordData = dictionaryResponse.data[0];
    const result = {
      word: wordData.word,
      phonetic: wordData.phonetic || wordData.phonetics?.[0]?.text || '',
      meanings: wordData.meanings.map(meaning => ({
        type: meaning.partOfSpeech,
        definitions: meaning.definitions.slice(0, 3).map(def => ({
          definition: def.definition,
          example: def.example || ''
        }))
      })),
      examples: [],
      phrases: []
    };

    // 收集例句
    wordData.meanings.forEach(meaning => {
      meaning.definitions.forEach(def => {
        if (def.example && result.examples.length < 5) {
          result.examples.push({
            english: def.example,
            chinese: `[示例] ${def.example}`
          });
        }
      });
    });

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('单词查询错误:', error.message);
    
    // 返回模拟数据
    const mockResult = {
      word: req.body.word,
      phonetic: `/${req.body.word}/`,
      meanings: [
        {
          type: 'noun',
          definitions: [
            {
              definition: '这是一个英语单词的基本释义',
              example: `This is an example sentence with ${req.body.word}.`
            }
          ]
        }
      ],
      examples: [
        {
          english: `This is an example sentence with ${req.body.word}.`,
          chinese: `这是一个包含 ${req.body.word} 的例句。`
        }
      ],
      phrases: [
        `common ${req.body.word}`,
        `${req.body.word} usage`
      ]
    };

    res.json({
      success: true,
      data: mockResult,
      note: 'This is a mock response due to API limitations'
    });
  }
});

// 翻译文本
router.post('/translate', async (req, res) => {
  try {
    const { text, from = 'en', to = 'zh' } = req.body;
    
    if (!text) {
      return res.status(400).json({ error: '翻译文本不能为空' });
    }

    // 使用免费的MyMemory翻译API
    const translateResponse = await axios.get(
      'https://api.mymemory.translated.net/get',
      {
        params: {
          q: text,
          langpair: `${from}|${to}`
        },
        timeout: 10000
      }
    );

    if (!translateResponse.data || !translateResponse.data.responseData) {
      throw new Error('翻译API响应格式错误');
    }

    const translation = translateResponse.data.responseData.translatedText;
    
    res.json({
      success: true,
      data: {
        original: text,
        translation,
        from,
        to,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('翻译错误:', error.message);
    
    // 返回模拟翻译
    const mockTranslations = {
      'Hello': '你好',
      'Good morning': '早上好',
      'Thank you': '谢谢',
      'How are you?': '你好吗？',
      'I love learning English': '我喜欢学习英语'
    };
    
    const translation = mockTranslations[text] || `[模拟翻译] ${text}`;
    
    res.json({
      success: true,
      data: {
        original: text,
        translation,
        from,
        to,
        timestamp: new Date().toISOString(),
        note: 'This is a mock response due to API limitations'
      }
    });
  }
});

// 处理完整文章（分句、提取单词等）
router.post('/process-article', async (req, res) => {
  try {
    const { article } = req.body;
    
    if (!article) {
      return res.status(400).json({ error: '文章内容不能为空' });
    }

    // 文本处理服务
    const textProcessService = require('../services/textProcessService');
    
    const processedData = {
      sentences: textProcessService.splitIntoSentences(article),
      words: textProcessService.extractWords(article),
      wordFrequency: textProcessService.getWordFrequency(article),
      keyPhrases: textProcessService.extractKeyPhrases(article),
      statistics: {
        totalWords: article.split(/\s+/).length,
        totalSentences: article.split(/[.!?]+/).length,
        averageWordsPerSentence: Math.round(article.split(/\s+/).length / article.split(/[.!?]+/).length)
      }
    };
    
    res.json({
      success: true,
      data: processedData
    });

  } catch (error) {
    console.error('文章处理错误:', error.message);
    res.status(500).json({ 
      error: '文章处理失败',
      message: error.message 
    });
  }
});

// 单词详情查询路由
router.get('/word-details/:word', async (req, res) => {
    const word = req.params.word;
    
    // 创建模拟数据函数（作为备用）
    const createMockData = (word) => {
        const mockTranslations = {
            'learning': '学习',
            'technology': '技术',
            'english': '英语',
            'computer': '计算机',
            'science': '科学',
            'education': '教育',
            'student': '学生',
            'teacher': '老师',
            'book': '书',
            'language': '语言'
        };
        
        const translation = mockTranslations[word.toLowerCase()] || `${word}的中文释义`;
        
        return {
            word: word,
            phonetic: `/${word}/`,
            meanings: [{
                type: 'noun',
                definitions: [{
                    definition: `A common English word: ${word}`,
                    example: `This is an example sentence with ${word}.`
                }, {
                    definition: `Another meaning of ${word}`,
                    example: `${word} is commonly used in English.`
                }]
            }],
            examples: [
                {
                    english: `This is an example sentence with ${word}.`,
                    chinese: `这是一个包含${word}的例句。`
                },
                {
                    english: `${word} is commonly used in English.`,
                    chinese: `${word}在英语中很常用。`
                },
                {
                    english: `Learning ${word} helps improve vocabulary.`,
                    chinese: `学习${word}有助于提高词汇量。`
                }
            ],
            phrases: [
                `common ${word}`,
                `${word} usage`,
                `learn ${word}`
            ]
        };
    };
    
    // 使用智谱AI获取单词详情
    const getWordDetailsFromAI = async (word) => {
        const prompt = `请为英语单词"${word}"提供详细信息，包括：
1. 音标（国际音标格式）
2. 中文释义
3. 词性
4. 3个英文例句及其中文翻译
5. 3个常用短语

请以JSON格式返回，格式如下：
{
  "word": "${word}",
  "phonetic": "/音标/",
  "translation": "中文释义",
  "partOfSpeech": "词性",
  "examples": [
    {"english": "英文例句", "chinese": "中文翻译"},
    {"english": "英文例句", "chinese": "中文翻译"},
    {"english": "英文例句", "chinese": "中文翻译"}
  ],
  "phrases": ["短语1", "短语2", "短语3"]
}`;
        
        const messages = [
            {
                role: "system",
                content: "你是一个专业的英语词典助手，能够提供准确的单词信息、音标、释义、例句和常用短语。请确保返回的是有效的JSON格式。"
            },
            {
                role: "user",
                content: prompt
            }
        ];
        
        try {
            const response = await callZhipuAPI(messages);
            // 尝试解析JSON响应
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const wordData = JSON.parse(jsonMatch[0]);
                return {
                    word: wordData.word || word,
                    phonetic: wordData.phonetic || `/${word}/`,
                    meanings: [{
                        type: wordData.partOfSpeech || 'noun',
                        definitions: [{
                            definition: wordData.translation || `${word}的释义`,
                            example: wordData.examples && wordData.examples[0] ? wordData.examples[0].english : `Example with ${word}.`
                        }]
                    }],
                    examples: wordData.examples || [
                        {
                            english: `This is an example with ${word}.`,
                            chinese: `这是一个包含${word}的例句。`
                        }
                    ],
                    phrases: wordData.phrases || [`common ${word}`, `${word} usage`]
                };
            } else {
                throw new Error('无法解析AI响应');
            }
        } catch (error) {
            console.error('智谱AI获取单词详情失败:', error.message);
            throw error;
        }
    };
    
    try {
        // 优先使用智谱AI获取单词详情
        console.log(`正在使用智谱AI查询单词: ${word}`);
        const aiWordData = await getWordDetailsFromAI(word);
        
        res.json({
            success: true,
            data: aiWordData,
            source: 'zhipu-ai'
        });
        
    } catch (aiError) {
        console.log('智谱AI查询失败，使用模拟数据:', aiError.message);
        
        // 如果智谱AI失败，使用模拟数据作为备用
        const mockData = createMockData(word);
        res.json({
            success: true,
            data: mockData,
            source: 'mock-data'
        });
    }
});

module.exports = router;